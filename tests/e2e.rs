use num::BigUint;
use plonky2::{
    field::goldilocks_field::Gold<PERSON>cks<PERSON>ield,
    plonk::{config::PoseidonGoldilocksConfig, proof::ProofWithPublicInputs},
};
use std::sync::Arc;

use intmax2_zkp::{
    circuits::{
        balance::{
            balance_pis::BalancePublicInputs, balance_processor::BalanceProcessor,
            receive::receive_targets::transfer_inclusion::TransferInclusionValue,
            send::spent_circuit::{SpentCircuit, SpentValue},
        },
        test_utils::{
            state_manager::ValidityStateManager,
            witness_generator::{construct_spent_and_transfer_witness, MockTxRequest},
        },
        validity::validity_processor::ValidityProcessor,
        withdrawal::single_withdrawal_circuit::SingleWithdrawalCircuit,
    },
    common::{
        deposit::{get_pubkey_salt_hash, Deposit},
        private_state::{FullPrivateState, PrivateState},
        salt::Salt,
        signature_content::key_set::KeySet,
        transfer::Transfer,
        trees::asset_tree::{AssetLeaf, AssetTree},
        witness::{
            deposit_witness::DepositWitness, private_transition_witness::PrivateTransitionWitness,
            receive_deposit_witness::ReceiveDepositWitness,
            receive_transfer_witness::ReceiveTransferWitness,
        },
    },
    constants::{ASSET_TREE_HEIGHT, NUM_TRANSFERS_IN_TX},
    ethereum_types::{address::Address, u256::U256, u32limb_trait::U32LimbTrait},
    utils::hash_chain::hash_chain_processor::HashChainProcessor,
};

type F = GoldilocksField;
type C = PoseidonGoldilocksConfig;
const D: usize = 2;

#[test]
fn test_e2e() {
    let mut rng = rand::thread_rng();
    let validity_processor = Arc::new(ValidityProcessor::<F, C, D>::new());
    let balance_processor = BalanceProcessor::new(&validity_processor.get_verifier_data());
    let mut validity_state_manager =
        ValidityStateManager::new(validity_processor.clone(), Address::default());
    let validity_vd = validity_processor.get_verifier_data();
    let balance_vd = balance_processor.get_verifier_data();

    let single_withdrawal_circuit = SingleWithdrawalCircuit::<F, C, D>::new(&balance_vd);
    let hash_chain_processor =
        HashChainProcessor::new(&single_withdrawal_circuit.data.verifier_data());

    let alice_key = KeySet::rand(&mut rng);
    let mut alice_private_state = FullPrivateState::new();
    let mut alice_balance_proof = None;

    let bob_key = KeySet::rand(&mut rng);
    let mut bob_private_state = FullPrivateState::new();
    let mut bob_balance_proof = None;

    // alice deposits 1 ETH
    let deposit_salt = Salt::rand(&mut rng);
    let deposit = Deposit {
        depositor: Address::rand(&mut rng),
        pubkey_salt_hash: get_pubkey_salt_hash(alice_key.pubkey, deposit_salt),
        amount: BigUint::from(10u32).pow(18).try_into().unwrap(),
        token_index: 0, // 0 is ETH
        is_eligible: true,
    };
    let deposit_index = validity_state_manager.deposit(&deposit).unwrap();

    // post empty block to sync the deposit tree
    validity_state_manager.tick(false, &[], 0, 0).unwrap();
    let receive_deposit_block_number = validity_state_manager.get_block_number();

    // update the public state to receive_deposit_block_number
    let alice_pis = get_balance_pis(alice_key.pubkey, &alice_balance_proof);
    let update_witness = validity_state_manager
        .get_update_witness(
            alice_key.pubkey,
            receive_deposit_block_number,
            alice_pis.public_state.block_number,
            false,
        )
        .unwrap();
    alice_balance_proof = Some(
        balance_processor
            .prove_update(
                &validity_vd,
                alice_key.pubkey,
                &update_witness,
                &alice_balance_proof,
            )
            .unwrap(),
    );

    // alice incorporates the deposit
    let alice_pis = get_balance_pis(alice_key.pubkey, &alice_balance_proof);
    let deposit_merkle_proof = validity_state_manager
        .get_deposit_merkle_proof(alice_pis.public_state.block_number, deposit_index)
        .unwrap();
    let deposit_witness = DepositWitness {
        deposit_salt,
        deposit_index,
        deposit: deposit.clone(),
        deposit_merkle_proof,
    };
    let private_transition_witness = PrivateTransitionWitness::from_deposit(
        &mut alice_private_state,
        &deposit,
        Salt::rand(&mut rng),
    )
    .unwrap();
    let receive_deposit_witness = ReceiveDepositWitness {
        deposit_witness,
        private_transition_witness,
    };
    alice_balance_proof = Some(
        balance_processor
            .prove_receive_deposit(
                alice_key.pubkey,
                &receive_deposit_witness,
                &alice_balance_proof,
            )
            .unwrap(),
    );

    // alice send 0.5 ETH to bob
    let transfer = Transfer {
        recipient: bob_key.pubkey.into(),
        token_index: 0,
        amount: BigUint::from(5u32).pow(17).try_into().unwrap(),
        salt: Salt::rand(&mut rng),
    };
    let (spent_witness, transfer_witnesses) =
        construct_spent_and_transfer_witness(&mut alice_private_state, &[transfer]).unwrap();
    let transfer_witness = transfer_witnesses[0].clone();
    let spent_proof = balance_processor.prove_spent(&spent_witness).unwrap();
    let tx_request = MockTxRequest {
        tx: spent_witness.tx,
        sender_key: alice_key,
        will_return_sig: true,
    };
    let tx_witnesses = validity_state_manager
        .tick(
            true, // since it's the first time to send a tx for alice, we use a registration block
            &[tx_request],
            0,
            0,
        )
        .unwrap();
    let tx_witness = tx_witnesses[0].clone();
    let transfer_block_number = validity_state_manager.get_block_number();

    // update alice's public state to transfer_block_number
    let alice_pis = get_balance_pis(alice_key.pubkey, &alice_balance_proof);
    let update_witness = validity_state_manager
        .get_update_witness(
            alice_key.pubkey,
            transfer_block_number,
            alice_pis.public_state.block_number,
            true,
        )
        .unwrap();
    alice_balance_proof = Some(
        balance_processor
            .prove_send(
                &validity_vd,
                alice_key.pubkey,
                &tx_witness,
                &update_witness,
                &spent_proof,
                &alice_balance_proof,
            )
            .unwrap(),
    );

    // bob update his public state to transfer_block_number
    let bob_pis = get_balance_pis(bob_key.pubkey, &bob_balance_proof);
    let update_witness = validity_state_manager
        .get_update_witness(
            bob_key.pubkey,
            transfer_block_number,
            bob_pis.public_state.block_number,
            false,
        )
        .unwrap();
    bob_balance_proof = Some(
        balance_processor
            .prove_update(
                &validity_vd,
                bob_key.pubkey,
                &update_witness,
                &bob_balance_proof,
            )
            .unwrap(),
    );

    // bob receives the transfer
    let private_transition_witness = PrivateTransitionWitness::from_transfer(
        &mut bob_private_state,
        transfer,
        Salt::rand(&mut rng),
    )
    .unwrap();
    let block_merkle_proof = validity_state_manager
        .get_block_merkle_proof(transfer_block_number, transfer_block_number)
        .unwrap();
    let receive_transfer_witness = ReceiveTransferWitness {
        transfer_witness,
        private_transition_witness,
        sender_balance_proof: alice_balance_proof.clone().unwrap(),
        block_merkle_proof,
    };
    let _bob_balance_proof = Some(
        balance_processor
            .prove_receive_transfer(
                bob_key.pubkey,
                &receive_transfer_witness,
                &bob_balance_proof,
            )
            .unwrap(),
    );

    // alice send withdrawal transfer of 0.1 ETH
    let withdrawal_transfer = Transfer {
        recipient: Address::rand(&mut rng).into(),
        token_index: 0,
        amount: BigUint::from(1u32).pow(17).try_into().unwrap(),
        salt: Salt::rand(&mut rng),
    };
    let (spent_witness, transfer_witnesses) =
        construct_spent_and_transfer_witness(&mut alice_private_state, &[withdrawal_transfer])
            .unwrap();
    let transfer_witness = transfer_witnesses[0].clone();
    let spent_proof = balance_processor.prove_spent(&spent_witness).unwrap();
    let tx_request = MockTxRequest {
        tx: spent_witness.tx,
        sender_key: alice_key,
        will_return_sig: true,
    };
    let tx_witnesses = validity_state_manager
        .tick(
            false, /* since it's the second time to send a tx for alice, we use a non
                    * registration block */
            &[tx_request],
            0,
            0,
        )
        .unwrap();
    let tx_witness = tx_witnesses[0].clone();
    let withdrawal_block_number = validity_state_manager.get_block_number();

    // update alice's public state to withdrawal_block_number
    let alice_pis = get_balance_pis(alice_key.pubkey, &alice_balance_proof);
    let update_witness = validity_state_manager
        .get_update_witness(
            alice_key.pubkey,
            withdrawal_block_number,
            alice_pis.public_state.block_number,
            true,
        )
        .unwrap();
    alice_balance_proof = Some(
        balance_processor
            .prove_send(
                &validity_vd,
                alice_key.pubkey,
                &tx_witness,
                &update_witness,
                &spent_proof,
                &alice_balance_proof,
            )
            .unwrap(),
    );

    // alice generates a single withdrawal proof
    let transfer_inclusion_value = TransferInclusionValue::new(
        &balance_vd,
        &transfer_witness.transfer,
        transfer_witness.transfer_index,
        &transfer_witness.transfer_merkle_proof,
        &transfer_witness.tx,
        &alice_balance_proof.unwrap(),
    )
    .unwrap();
    let single_withdrawal_proof = single_withdrawal_circuit
        .prove(&transfer_inclusion_value)
        .unwrap();

    // generate a withdrawal chain
    let chain_withdrawal_proof = hash_chain_processor
        .prove_chain(&single_withdrawal_proof, &None)
        .unwrap();
    let end_withdrawal_proof = hash_chain_processor
        .prove_end(&chain_withdrawal_proof, Address::rand(&mut rng))
        .unwrap();
    hash_chain_processor
        .chain_end_circuit
        .data
        .verify(end_withdrawal_proof)
        .unwrap();
}

fn get_balance_pis(
    pubkey: U256,
    proof: &Option<ProofWithPublicInputs<F, C, D>>,
) -> BalancePublicInputs {
    proof
        .as_ref()
        .map(|p| BalancePublicInputs::from_pis(&p.public_inputs).unwrap())
        .unwrap_or(BalancePublicInputs::new(pubkey))
}

#[test]
fn test_cross_verifier_proof_compatibility() {
    let mut rng = rand::thread_rng();

    // Deploy two identical verifier contracts (A and B)
    let verifier_a = SpentCircuit::<F, C, D>::new();
    let verifier_b = SpentCircuit::<F, C, D>::new();

    // Get verifier data from both contracts
    let vd_a = verifier_a.data.verifier_data();
    let vd_b = verifier_b.data.verifier_data();

    // Print circuit_digest and constants_sigmas_cap for both verifiers
    println!("Verifier A circuit_digest: {:?}", vd_a.verifier_only.circuit_digest);
    println!("Verifier B circuit_digest: {:?}", vd_b.verifier_only.circuit_digest);

    println!("Verifier A constants_sigmas_cap: {:?}", vd_a.verifier_only.constants_sigmas_cap);
    println!("Verifier B constants_sigmas_cap: {:?}", vd_b.verifier_only.constants_sigmas_cap);

    // Verify that circuit_digest and constants_sigmas_cap match between A and B
    assert_eq!(vd_a.verifier_only.circuit_digest, vd_b.verifier_only.circuit_digest,
               "Circuit digests should match between identical deployments");
    assert_eq!(vd_a.verifier_only.constants_sigmas_cap, vd_b.verifier_only.constants_sigmas_cap,
               "Constants sigmas cap should match between identical deployments");

    // Create test data for proof generation
    let mut asset_tree = AssetTree::new(ASSET_TREE_HEIGHT);
    let prev_balances = (0..NUM_TRANSFERS_IN_TX)
        .map(|_| AssetLeaf::rand(&mut rng))
        .collect::<Vec<_>>();

    // Update asset tree with balances first
    for (i, balance) in prev_balances.iter().enumerate() {
        asset_tree.update(i as u64, *balance);
    }

    // Get the root after all updates
    let asset_tree_root = asset_tree.get_root();

    let prev_private_state = PrivateState {
        asset_tree_root,
        nonce: 12,
        ..PrivateState::new()
    };

    let transfers = (0..NUM_TRANSFERS_IN_TX)
        .map(|i| Transfer {
            recipient: U256::rand(&mut rng).into(),
            token_index: i as u32,
            amount: U256::rand_small(&mut rng), // small amount to avoid overflow
            salt: Salt::rand(&mut rng),
        })
        .collect::<Vec<_>>();

    // Generate merkle proofs for each transfer's token_index
    let mut asset_merkle_proofs = Vec::with_capacity(NUM_TRANSFERS_IN_TX);
    for (index, (transfer, prev_balance)) in
        transfers.iter().zip(prev_balances.iter()).enumerate()
    {
        assert_eq!(*prev_balance, asset_tree.get_leaf(index as u64));
        let proof = asset_tree.prove(transfer.token_index as u64);
        let new_balance = prev_balance.sub(transfer.amount);
        asset_tree.update(transfer.token_index as u64, new_balance);
        asset_merkle_proofs.push(proof);
    }

    let new_private_state_salt = Salt::rand(&mut rng);
    let spent_value = SpentValue::new(
        &prev_private_state,
        &prev_balances,
        new_private_state_salt,
        &transfers,
        &asset_merkle_proofs,
        prev_private_state.nonce,
    )
    .expect("Failed to create spent value");

    assert!(spent_value.is_valid, "Spent value should be valid");

    // Generate proof against verifier A
    let proof_from_a = verifier_a.prove(&spent_value)
        .expect("Failed to generate proof with verifier A");

    // Verify the proof on verifier A (should work)
    verifier_a.data.verify(proof_from_a.clone())
        .expect("Proof should verify on original verifier A");

    // Verify the exact same proof blob on verifier B (should also work)
    verifier_b.data.verify(proof_from_a)
        .expect("Proof generated by verifier A should verify on verifier B");

    println!("✓ Proof generated by verifier A successfully verified on verifier B");
}
