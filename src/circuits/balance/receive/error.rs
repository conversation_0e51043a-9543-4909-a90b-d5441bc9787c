use thiserror::Error;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>)]
pub enum ReceiveError {
    #[error("Verification failed: {0}")]
    VerificationFailed(String),

    #[error("Invalid input: {0}")]
    InvalidInput(String),

    #[error("Proof generation error: {0}")]
    ProofGenerationError(String),
}

#[derive(Debu<PERSON>, Erro<PERSON>)]
pub enum UpdateError {
    #[error("Verification failed: {0}")]
    VerificationFailed(String),

    #[error("Invalid input: {0}")]
    InvalidInput(String),

    #[error("Proof generation error: {0}")]
    ProofGenerationError(String),
}
