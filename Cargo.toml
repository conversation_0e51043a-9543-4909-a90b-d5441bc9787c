[package]
name = "intmax2-zkp"
version = "0.1.0"
edition = "2021"

[dependencies]
plonky2 = { git = "https://github.com/InternetMaximalism/polygon-plonky2.git", branch = "intmax2-dev" }
plonky2_u32 = { git = "https://github.com/InternetMaximalism/plonky2-u32.git", branch = "intmax2-dev" }
plonky2_keccak = { git = "https://github.com/InternetMaximalism/plonky2_keccak" }
plonky2_bn254 = { git = "https://github.com/InternetMaximalism/plonky2_bn254" }
hex = "0.4.3"
rand = "0.8.5"
anyhow = "1.0.97"
itertools = "0.12.1"
thiserror = "1.0.57"
log = "0.4.21"
num = "0.4.3"
num-bigint = "0.4.5"
ark-bn254 = "0.4.0"
ark-ec = "0.4.2"
ark-ff = "0.4.2"
ark-std = "0.4.0"
serde = "1.0.219"
serde_json = "1.0.140"
ff = { package = "ff", version = "0.13", features = ["derive"] }
lazy_static = "1.5.0"
bincode = "1.3.3"
base64 = "0.22.1"

[features]
default = []
dummy_validity_proof = []
skip_insufficient_check = []
